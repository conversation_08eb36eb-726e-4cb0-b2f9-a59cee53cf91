<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_72b3949dd8d21c6457ad28060c5793bd {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
        <div style="position: fixed; 
                    top: 10px; right: 10px; width: 200px; height: 120px; 
                    background-color: white; border:2px solid grey; z-index:9999; 
                    font-size:14px; padding: 10px">
        <h4>Region: USA</h4>
        <p><b>Airports:</b> 14</p>
        <p><b>Procedures:</b> 1633</p>
        <p><b>SIDs:</b> 992</p>
        <p><b>STARs:</b> 641</p>
        </div>
        
    
            <div class="folium-map" id="map_72b3949dd8d21c6457ad28060c5793bd" ></div>
        
</body>
<script>
    
    
            var map_72b3949dd8d21c6457ad28060c5793bd = L.map(
                "map_72b3949dd8d21c6457ad28060c5793bd",
                {
                    center: [38.248264880952384, -101.60351468253967],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 6,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_fc7554bc3ecd335d5ae6fad3fe5eedfb = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_fc7554bc3ecd335d5ae6fad3fe5eedfb.addTo(map_72b3949dd8d21c6457ad28060c5793bd);
        
    
            var tile_layer_deb9d041e4d939cd01906d501706f3df = L.tileLayer(
                "https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 18,
  "maxNativeZoom": 18,
  "noWrap": false,
  "attribution": "Map tiles by Stamen Design, CC BY 3.0 \u2014 Map data \u00a9 OpenStreetMap contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_deb9d041e4d939cd01906d501706f3df.addTo(map_72b3949dd8d21c6457ad28060c5793bd);
        
    
            var tile_layer_8e3e4166825a923904a3fa15c7612090 = L.tileLayer(
                "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
                {
  "minZoom": 0,
  "maxZoom": 20,
  "maxNativeZoom": 20,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors \u0026copy; \u003ca href=\"https://carto.com/attributions\"\u003eCARTO\u003c/a\u003e",
  "subdomains": "abcd",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_8e3e4166825a923904a3fa15c7612090.addTo(map_72b3949dd8d21c6457ad28060c5793bd);
        
    
            var layer_control_c56f2649f03330e5a31cfdd0f28f8043_layers = {
                base_layers : {
                    "openstreetmap" : tile_layer_fc7554bc3ecd335d5ae6fad3fe5eedfb,
                    "Stamen Terrain" : tile_layer_deb9d041e4d939cd01906d501706f3df,
                    "CartoDB Positron" : tile_layer_8e3e4166825a923904a3fa15c7612090,
                },
                overlays :  {
                    "Airports" : feature_group_dbff449e3da26fd37c27f7cafd70f4cd,
                },
            };
            let layer_control_c56f2649f03330e5a31cfdd0f28f8043 = L.control.layers(
                layer_control_c56f2649f03330e5a31cfdd0f28f8043_layers.base_layers,
                layer_control_c56f2649f03330e5a31cfdd0f28f8043_layers.overlays,
                {
  "position": "topright",
  "collapsed": true,
  "autoZIndex": true,
}
            ).addTo(map_72b3949dd8d21c6457ad28060c5793bd);

        
    
            var feature_group_dbff449e3da26fd37c27f7cafd70f4cd = L.featureGroup(
                {
}
            );
        
    
            var marker_b23e19913aa669ba1b652b1429107a6c = L.marker(
                [37.618805555555554, -122.37541666666667],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_4955ba101f31acd0474c0607bd5e9b79 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_a4929faba36873bc556dcdf334cb6e35 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_16a1eca0ff423f3e5c7b91797d067c66 = $(`<div id="html_16a1eca0ff423f3e5c7b91797d067c66" style="width: 100.0%; height: 100.0%;">             <b>SAN FRANCISCO INTL</b><br>             ICAO: KSFO<br>             Region: USA<br>             Coordinates: 37.618806, -122.375417             </div>`)[0];
                popup_a4929faba36873bc556dcdf334cb6e35.setContent(html_16a1eca0ff423f3e5c7b91797d067c66);
            
        

        marker_b23e19913aa669ba1b652b1429107a6c.bindPopup(popup_a4929faba36873bc556dcdf334cb6e35)
        ;

        
    
    
            marker_b23e19913aa669ba1b652b1429107a6c.bindTooltip(
                `<div>
                     KSFO - SAN FRANCISCO INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_b23e19913aa669ba1b652b1429107a6c.setIcon(icon_4955ba101f31acd0474c0607bd5e9b79);
            
    
            var marker_c703b9349d08c2d2f2020d8c13514aad = L.marker(
                [36.101327777777776, -79.94112222222222],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_03fabc8f146454d3773bfd039a3b3cb7 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_98b6aea95631505e258a4d71aca8783e = L.popup({
  "maxWidth": 300,
});

        
            
                var html_bb42a3f887461b14406dd07d6971cbc5 = $(`<div id="html_bb42a3f887461b14406dd07d6971cbc5" style="width: 100.0%; height: 100.0%;">             <b>PIEDMONT TRIAD INTL</b><br>             ICAO: KGSO<br>             Region: USA<br>             Coordinates: 36.101328, -79.941122             </div>`)[0];
                popup_98b6aea95631505e258a4d71aca8783e.setContent(html_bb42a3f887461b14406dd07d6971cbc5);
            
        

        marker_c703b9349d08c2d2f2020d8c13514aad.bindPopup(popup_98b6aea95631505e258a4d71aca8783e)
        ;

        
    
    
            marker_c703b9349d08c2d2f2020d8c13514aad.bindTooltip(
                `<div>
                     KGSO - PIEDMONT TRIAD INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_c703b9349d08c2d2f2020d8c13514aad.setIcon(icon_03fabc8f146454d3773bfd039a3b3cb7);
            
    
            var marker_095d405b85391d283400f6479489dfc2 = L.marker(
                [33.94249722222222, -118.40805],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_685d7caa1b2a4939b2e331ef7bedea8c = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_8654c0eaee652a357b3476933f68ee80 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_e5de5fc9705e69107279f8f1ac6176a4 = $(`<div id="html_e5de5fc9705e69107279f8f1ac6176a4" style="width: 100.0%; height: 100.0%;">             <b>LOS ANGELES INTL</b><br>             ICAO: KLAX<br>             Region: USA<br>             Coordinates: 33.942497, -118.408050             </div>`)[0];
                popup_8654c0eaee652a357b3476933f68ee80.setContent(html_e5de5fc9705e69107279f8f1ac6176a4);
            
        

        marker_095d405b85391d283400f6479489dfc2.bindPopup(popup_8654c0eaee652a357b3476933f68ee80)
        ;

        
    
    
            marker_095d405b85391d283400f6479489dfc2.bindTooltip(
                `<div>
                     KLAX - LOS ANGELES INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_095d405b85391d283400f6479489dfc2.setIcon(icon_685d7caa1b2a4939b2e331ef7bedea8c);
            
    
            var marker_afca05ece04170dc39be5b64bc526d39 = L.marker(
                [38.53433333333333, -106.93175000000001],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_c6e011ec2d4c42c69105aabd902bd36e = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_88c0308500aaa5e35b81abab20bfeeec = L.popup({
  "maxWidth": 300,
});

        
            
                var html_cb743c7e0df72a80f10f339e9404dc2d = $(`<div id="html_cb743c7e0df72a80f10f339e9404dc2d" style="width: 100.0%; height: 100.0%;">             <b>GUNNISON-CRESTED BUTTE REGL</b><br>             ICAO: KGUC<br>             Region: USA<br>             Coordinates: 38.534333, -106.931750             </div>`)[0];
                popup_88c0308500aaa5e35b81abab20bfeeec.setContent(html_cb743c7e0df72a80f10f339e9404dc2d);
            
        

        marker_afca05ece04170dc39be5b64bc526d39.bindPopup(popup_88c0308500aaa5e35b81abab20bfeeec)
        ;

        
    
    
            marker_afca05ece04170dc39be5b64bc526d39.bindTooltip(
                `<div>
                     KGUC - GUNNISON-CRESTED BUTTE REGL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_afca05ece04170dc39be5b64bc526d39.setIcon(icon_c6e011ec2d4c42c69105aabd902bd36e);
            
    
            var marker_de6b3b1acd21eba63d4f09015b3bcc02 = L.marker(
                [35.04241111111111, -89.97668055555556],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_a6f4b9166294e7604333857f9ae9f556 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_f104a6b11251725dd343943e157c1271 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_cd5db4f3b1720fc3ada7d1a6b7cabba6 = $(`<div id="html_cd5db4f3b1720fc3ada7d1a6b7cabba6" style="width: 100.0%; height: 100.0%;">             <b>MEMPHIS INTL</b><br>             ICAO: KMEM<br>             Region: USA<br>             Coordinates: 35.042411, -89.976681             </div>`)[0];
                popup_f104a6b11251725dd343943e157c1271.setContent(html_cd5db4f3b1720fc3ada7d1a6b7cabba6);
            
        

        marker_de6b3b1acd21eba63d4f09015b3bcc02.bindPopup(popup_f104a6b11251725dd343943e157c1271)
        ;

        
    
    
            marker_de6b3b1acd21eba63d4f09015b3bcc02.bindTooltip(
                `<div>
                     KMEM - MEMPHIS INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_de6b3b1acd21eba63d4f09015b3bcc02.setIcon(icon_a6f4b9166294e7604333857f9ae9f556);
            
    
            var marker_8ca72c7b2a30e95b14b0d09169eb3a61 = L.marker(
                [47.449888888888886, -122.31177777777778],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_9b8e7507dcb5f115b6278e69afed2252 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_8ef056b85f14bd43101105ca9e8cf36e = L.popup({
  "maxWidth": 300,
});

        
            
                var html_3c837cc10f1899f23a37767967409c3c = $(`<div id="html_3c837cc10f1899f23a37767967409c3c" style="width: 100.0%; height: 100.0%;">             <b>SEATTLE-TACOMA INTL</b><br>             ICAO: KSEA<br>             Region: USA<br>             Coordinates: 47.449889, -122.311778             </div>`)[0];
                popup_8ef056b85f14bd43101105ca9e8cf36e.setContent(html_3c837cc10f1899f23a37767967409c3c);
            
        

        marker_8ca72c7b2a30e95b14b0d09169eb3a61.bindPopup(popup_8ef056b85f14bd43101105ca9e8cf36e)
        ;

        
    
    
            marker_8ca72c7b2a30e95b14b0d09169eb3a61.bindTooltip(
                `<div>
                     KSEA - SEATTLE-TACOMA INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_8ca72c7b2a30e95b14b0d09169eb3a61.setIcon(icon_9b8e7507dcb5f115b6278e69afed2252);
            
    
            var marker_e3db255564949a181cf94656e93f158b = L.marker(
                [40.85011111111111, -74.06083333333333],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_aef23245538c73b124a7e6e22adbd189 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_e8f5473ca795324d7095520c817c902f = L.popup({
  "maxWidth": 300,
});

        
            
                var html_c6a40cfe3d81a32c4cc408d0bdf2dd9f = $(`<div id="html_c6a40cfe3d81a32c4cc408d0bdf2dd9f" style="width: 100.0%; height: 100.0%;">             <b>TETERBORO</b><br>             ICAO: KTEB<br>             Region: USA<br>             Coordinates: 40.850111, -74.060833             </div>`)[0];
                popup_e8f5473ca795324d7095520c817c902f.setContent(html_c6a40cfe3d81a32c4cc408d0bdf2dd9f);
            
        

        marker_e3db255564949a181cf94656e93f158b.bindPopup(popup_e8f5473ca795324d7095520c817c902f)
        ;

        
    
    
            marker_e3db255564949a181cf94656e93f158b.bindTooltip(
                `<div>
                     KTEB - TETERBORO
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_e3db255564949a181cf94656e93f158b.setIcon(icon_aef23245538c73b124a7e6e22adbd189);
            
    
            var marker_bcbedeb27f73a0e31c1291d3271f69dc = L.marker(
                [39.22188888888889, -106.86822222222222],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_487c45734bf150645f1284fa0a3fc812 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_57ba573a217ec92b67cdc3b3deb0e5c7 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_816f6fc29f6a2bc1863bf03d6fc50b78 = $(`<div id="html_816f6fc29f6a2bc1863bf03d6fc50b78" style="width: 100.0%; height: 100.0%;">             <b>ASPEN-PITKIN CO/SARDY</b><br>             ICAO: KASE<br>             Region: USA<br>             Coordinates: 39.221889, -106.868222             </div>`)[0];
                popup_57ba573a217ec92b67cdc3b3deb0e5c7.setContent(html_816f6fc29f6a2bc1863bf03d6fc50b78);
            
        

        marker_bcbedeb27f73a0e31c1291d3271f69dc.bindPopup(popup_57ba573a217ec92b67cdc3b3deb0e5c7)
        ;

        
    
    
            marker_bcbedeb27f73a0e31c1291d3271f69dc.bindTooltip(
                `<div>
                     KASE - ASPEN-PITKIN CO/SARDY
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_bcbedeb27f73a0e31c1291d3271f69dc.setIcon(icon_487c45734bf150645f1284fa0a3fc812);
            
    
            var marker_a8beb4ea71d14794e63083a9e9ce9d83 = L.marker(
                [33.43427777777777, -112.01158333333333],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_d76aaec910134fd92664d4a913954b00 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_88945a7877e55ca8ab85984684e2e91e = L.popup({
  "maxWidth": 300,
});

        
            
                var html_3bbb8714bc3c6eb0a810e8cbf63429c9 = $(`<div id="html_3bbb8714bc3c6eb0a810e8cbf63429c9" style="width: 100.0%; height: 100.0%;">             <b>PHOENIX SKY HARBOR INTL</b><br>             ICAO: KPHX<br>             Region: USA<br>             Coordinates: 33.434278, -112.011583             </div>`)[0];
                popup_88945a7877e55ca8ab85984684e2e91e.setContent(html_3bbb8714bc3c6eb0a810e8cbf63429c9);
            
        

        marker_a8beb4ea71d14794e63083a9e9ce9d83.bindPopup(popup_88945a7877e55ca8ab85984684e2e91e)
        ;

        
    
    
            marker_a8beb4ea71d14794e63083a9e9ce9d83.bindTooltip(
                `<div>
                     KPHX - PHOENIX SKY HARBOR INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_a8beb4ea71d14794e63083a9e9ce9d83.setIcon(icon_d76aaec910134fd92664d4a913954b00);
            
    
            var marker_0271c0e0d4e68feb74de5a1e35149f69 = L.marker(
                [39.49911111111111, -119.76811111111111],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_ad1ade828e04b8ee32fa624c281cf33b = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_447963e53f81446b9ecbdc480112a717 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_6493f2852c86dab5cd3344356be16de3 = $(`<div id="html_6493f2852c86dab5cd3344356be16de3" style="width: 100.0%; height: 100.0%;">             <b>RENO/TAHOE INTL</b><br>             ICAO: KRNO<br>             Region: USA<br>             Coordinates: 39.499111, -119.768111             </div>`)[0];
                popup_447963e53f81446b9ecbdc480112a717.setContent(html_6493f2852c86dab5cd3344356be16de3);
            
        

        marker_0271c0e0d4e68feb74de5a1e35149f69.bindPopup(popup_447963e53f81446b9ecbdc480112a717)
        ;

        
    
    
            marker_0271c0e0d4e68feb74de5a1e35149f69.bindTooltip(
                `<div>
                     KRNO - RENO/TAHOE INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_0271c0e0d4e68feb74de5a1e35149f69.setIcon(icon_ad1ade828e04b8ee32fa624c281cf33b);
            
    
            var marker_b7c976982dc20d1b6b30296ff0234e87 = L.marker(
                [39.861666666666665, -104.67316666666667],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_855b371404d8ab246f0626d7519c96b3 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_3ae793a5a3891dee65ddece2994b3e59 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_cbbb4fece6df3ee458348d5bdf48cd70 = $(`<div id="html_cbbb4fece6df3ee458348d5bdf48cd70" style="width: 100.0%; height: 100.0%;">             <b>DENVER INTL</b><br>             ICAO: KDEN<br>             Region: USA<br>             Coordinates: 39.861667, -104.673167             </div>`)[0];
                popup_3ae793a5a3891dee65ddece2994b3e59.setContent(html_cbbb4fece6df3ee458348d5bdf48cd70);
            
        

        marker_b7c976982dc20d1b6b30296ff0234e87.bindPopup(popup_3ae793a5a3891dee65ddece2994b3e59)
        ;

        
    
    
            marker_b7c976982dc20d1b6b30296ff0234e87.bindTooltip(
                `<div>
                     KDEN - DENVER INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_b7c976982dc20d1b6b30296ff0234e87.setIcon(icon_855b371404d8ab246f0626d7519c96b3);
            
    
            var marker_29fb2a016f66ebf15873469735e71fc2 = L.marker(
                [39.64276111111111, -106.91593611111112],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_ef48810637e7df8d4eb7af2a851ac264 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_73819d833e6f3589e48c3135da2e0295 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_840e5a4be922864c7ecefa1fd0f8712c = $(`<div id="html_840e5a4be922864c7ecefa1fd0f8712c" style="width: 100.0%; height: 100.0%;">             <b>EAGLE CO REGL</b><br>             ICAO: KEGE<br>             Region: USA<br>             Coordinates: 39.642761, -106.915936             </div>`)[0];
                popup_73819d833e6f3589e48c3135da2e0295.setContent(html_840e5a4be922864c7ecefa1fd0f8712c);
            
        

        marker_29fb2a016f66ebf15873469735e71fc2.bindPopup(popup_73819d833e6f3589e48c3135da2e0295)
        ;

        
    
    
            marker_29fb2a016f66ebf15873469735e71fc2.bindTooltip(
                `<div>
                     KEGE - EAGLE CO REGL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_29fb2a016f66ebf15873469735e71fc2.setIcon(icon_ef48810637e7df8d4eb7af2a851ac264);
            
    
            var marker_219c019d7686cf78b01e4ff94f7e5ce4 = L.marker(
                [33.6367, -84.4278638888889],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_060ccf3b62187fa2c4e71fcf1414829f = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_2bcf455b6efc5fddd60696a2c951607b = L.popup({
  "maxWidth": 300,
});

        
            
                var html_63bbc3e0745ee1c164ecd65eec1c1405 = $(`<div id="html_63bbc3e0745ee1c164ecd65eec1c1405" style="width: 100.0%; height: 100.0%;">             <b>HARTSFIELD - JACKSON ATLANTA I</b><br>             ICAO: KATL<br>             Region: USA<br>             Coordinates: 33.636700, -84.427864             </div>`)[0];
                popup_2bcf455b6efc5fddd60696a2c951607b.setContent(html_63bbc3e0745ee1c164ecd65eec1c1405);
            
        

        marker_219c019d7686cf78b01e4ff94f7e5ce4.bindPopup(popup_2bcf455b6efc5fddd60696a2c951607b)
        ;

        
    
    
            marker_219c019d7686cf78b01e4ff94f7e5ce4.bindTooltip(
                `<div>
                     KATL - HARTSFIELD - JACKSON ATLANTA I
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_219c019d7686cf78b01e4ff94f7e5ce4.setIcon(icon_060ccf3b62187fa2c4e71fcf1414829f);
            
    
            var marker_df6c5e480cb67326deb33a115ac6bf8d = L.marker(
                [40.63992777777778, -73.77869166666666],
                {
}
            ).addTo(feature_group_dbff449e3da26fd37c27f7cafd70f4cd);
        
    
            var icon_fed1773f858955750cf031487138be5e = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_2a19e01c982ada04a827e01ec23d77be = L.popup({
  "maxWidth": 300,
});

        
            
                var html_1f217debdf5f4663c18f928010b8ea0e = $(`<div id="html_1f217debdf5f4663c18f928010b8ea0e" style="width: 100.0%; height: 100.0%;">             <b>KENNEDY INTL</b><br>             ICAO: KJFK<br>             Region: USA<br>             Coordinates: 40.639928, -73.778692             </div>`)[0];
                popup_2a19e01c982ada04a827e01ec23d77be.setContent(html_1f217debdf5f4663c18f928010b8ea0e);
            
        

        marker_df6c5e480cb67326deb33a115ac6bf8d.bindPopup(popup_2a19e01c982ada04a827e01ec23d77be)
        ;

        
    
    
            marker_df6c5e480cb67326deb33a115ac6bf8d.bindTooltip(
                `<div>
                     KJFK - KENNEDY INTL
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_df6c5e480cb67326deb33a115ac6bf8d.setIcon(icon_fed1773f858955750cf031487138be5e);
            
    
            feature_group_dbff449e3da26fd37c27f7cafd70f4cd.addTo(map_72b3949dd8d21c6457ad28060c5793bd);
        
    
            map_72b3949dd8d21c6457ad28060c5793bd.fitBounds(
                [[32.03271666666666, -127.23508916666667], [48.85145, -68.91901916666666]],
                {}
            );
        
</script>
</html>