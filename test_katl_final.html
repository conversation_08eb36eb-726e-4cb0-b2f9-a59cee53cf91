<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_93216abb10ae06e0c4c409ec1bba1de8 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_93216abb10ae06e0c4c409ec1bba1de8" ></div>
        
</body>
<script>
    
    
            var map_93216abb10ae06e0c4c409ec1bba1de8 = L.map(
                "map_93216abb10ae06e0c4c409ec1bba1de8",
                {
                    center: [33.6367, -84.4278638888889],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 6,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_ed5409adb65043e9df0fab67171a256a = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_ed5409adb65043e9df0fab67171a256a.addTo(map_93216abb10ae06e0c4c409ec1bba1de8);
        
    
            var tile_layer_13f7d7a625d6d02dd9aa6fc6fb63e02a = L.tileLayer(
                "https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 18,
  "maxNativeZoom": 18,
  "noWrap": false,
  "attribution": "Map tiles by Stamen Design, CC BY 3.0 \u2014 Map data \u00a9 OpenStreetMap contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_13f7d7a625d6d02dd9aa6fc6fb63e02a.addTo(map_93216abb10ae06e0c4c409ec1bba1de8);
        
    
            var tile_layer_695e6b39899db9501db69701ed3afa0b = L.tileLayer(
                "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
                {
  "minZoom": 0,
  "maxZoom": 20,
  "maxNativeZoom": 20,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors \u0026copy; \u003ca href=\"https://carto.com/attributions\"\u003eCARTO\u003c/a\u003e",
  "subdomains": "abcd",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_695e6b39899db9501db69701ed3afa0b.addTo(map_93216abb10ae06e0c4c409ec1bba1de8);
        
    
            var layer_control_d118f04dedc2a0cb8e7c49b9d7d5c86d_layers = {
                base_layers : {
                    "openstreetmap" : tile_layer_ed5409adb65043e9df0fab67171a256a,
                    "Stamen Terrain" : tile_layer_13f7d7a625d6d02dd9aa6fc6fb63e02a,
                    "CartoDB Positron" : tile_layer_695e6b39899db9501db69701ed3afa0b,
                },
                overlays :  {
                    "Airports" : feature_group_cfb0203f04216a662e910178085a8065,
                },
            };
            let layer_control_d118f04dedc2a0cb8e7c49b9d7d5c86d = L.control.layers(
                layer_control_d118f04dedc2a0cb8e7c49b9d7d5c86d_layers.base_layers,
                layer_control_d118f04dedc2a0cb8e7c49b9d7d5c86d_layers.overlays,
                {
  "position": "topright",
  "collapsed": true,
  "autoZIndex": true,
}
            ).addTo(map_93216abb10ae06e0c4c409ec1bba1de8);

        
    
            var feature_group_cfb0203f04216a662e910178085a8065 = L.featureGroup(
                {
}
            );
        
    
            var marker_0542b285fa84c1359988ec0a6ccd3ec9 = L.marker(
                [33.6367, -84.4278638888889],
                {
}
            ).addTo(feature_group_cfb0203f04216a662e910178085a8065);
        
    
            var icon_e2f8f18aefba34c63393a28b19cb92ae = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_bff203e5ac889adb9bc72f4c2e0ef9ec = L.popup({
  "maxWidth": 300,
});

        
            
                var html_aa63c7f1b82a9da7162ec4d68e7567f3 = $(`<div id="html_aa63c7f1b82a9da7162ec4d68e7567f3" style="width: 100.0%; height: 100.0%;">             <b>HARTSFIELD - JACKSON ATLANTA I</b><br>             ICAO: KATL<br>             Region: USA<br>             Coordinates: 33.636700, -84.427864             </div>`)[0];
                popup_bff203e5ac889adb9bc72f4c2e0ef9ec.setContent(html_aa63c7f1b82a9da7162ec4d68e7567f3);
            
        

        marker_0542b285fa84c1359988ec0a6ccd3ec9.bindPopup(popup_bff203e5ac889adb9bc72f4c2e0ef9ec)
        ;

        
    
    
            marker_0542b285fa84c1359988ec0a6ccd3ec9.bindTooltip(
                `<div>
                     KATL - HARTSFIELD - JACKSON ATLANTA I
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_0542b285fa84c1359988ec0a6ccd3ec9.setIcon(icon_e2f8f18aefba34c63393a28b19cb92ae);
            
    
            feature_group_cfb0203f04216a662e910178085a8065.addTo(map_93216abb10ae06e0c4c409ec1bba1de8);
        
    
            map_93216abb10ae06e0c4c409ec1bba1de8.fitBounds(
                [[33.6367, -84.4278638888889], [33.6367, -84.4278638888889]],
                {}
            );
        
</script>
</html>