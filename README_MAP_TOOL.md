# ARINC424 Map Visualization Tool

This tool parses ARINC424 navigation data files (.pc files) and creates interactive maps showing airports, waypoints, SIDs, STARs, and their relationships.

## Features

- **Region-based filtering**: Filter airports and procedures by region (e.g., USA, CAN, SAM)
- **ICAO-based filtering**: Filter by specific airport ICAO codes
- **SID/STAR visualization**: Plot Standard Instrument Departures and Standard Terminal Arrival Routes with waypoint sequences
- **Waypoint resolution**: Automatically find and plot waypoints referenced in procedures
- **Interactive maps**: Clickable markers with detailed information
- **Multiple output formats**: HTML maps that can be opened in any web browser

## Installation

1. Make sure you have Python 3.10+ installed
2. Install required dependencies:
   ```bash
   pip install folium
   ```
3. The tool uses the existing ARINC424 library in the `src/` directory

## Usage

### Basic Commands

#### List available regions:
```bash
python3 arinc424_map_tool.py flyright.pc --list-regions
```

#### List airports in a region:
```bash
python3 arinc424_map_tool.py flyright.pc --region USA --list-airports
```

#### Show airports on a map:
```bash
python3 arinc424_map_tool.py flyright.pc --region USA --show-airports
```

#### Show procedures for specific airports:
```bash
python3 arinc424_map_tool.py flyright.pc --icao KATL KORD --show-procedures
```

#### Show everything for a region:
```bash
python3 arinc424_map_tool.py flyright.pc --region CAN --show-all
```

### Advanced Filtering

#### Show only SIDs for an airport:
```bash
python3 arinc424_map_tool.py flyright.pc --icao KATL --show-procedures --procedure-types SID
```

#### Show only STARs for multiple airports:
```bash
python3 arinc424_map_tool.py flyright.pc --icao KATL KORD KLAX --show-procedures --procedure-types STAR
```

#### Custom output filename:
```bash
python3 arinc424_map_tool.py flyright.pc --icao KATL --show-all --output atlanta_procedures.html
```

## Command Line Options

### Required Arguments
- `file`: ARINC424 file to parse (e.g., flyright.pc)

### Filtering Options
- `--region REGION`: Filter by region code (e.g., USA, CAN, SAM)
- `--icao ICAO [ICAO ...]`: Filter by ICAO airport codes (e.g., KATL KORD)

### Display Options
- `--show-airports`: Show airports on the map
- `--show-waypoints`: Show waypoints on the map
- `--show-procedures`: Show SID/STAR procedures on the map
- `--show-all`: Show airports, waypoints, and procedures

### Procedure Filtering
- `--procedure-types {SID,STAR,APPROACH} [{SID,STAR,APPROACH} ...]`: Types of procedures to show (default: SID STAR)

### Output Options
- `--output OUTPUT`, `-o OUTPUT`: Output HTML file name (default: arinc424_map.html)

### Information Options
- `--list-regions`: List all available regions and exit
- `--list-airports`: List airports in the specified region/ICAOs and exit

## Examples

### Example 1: Explore Atlanta Airport (KATL)
```bash
# List all procedures for Atlanta
python3 arinc424_map_tool.py flyright.pc --icao KATL --show-all --output katl_complete.html

# Show only SIDs for Atlanta
python3 arinc424_map_tool.py flyright.pc --icao KATL --show-procedures --procedure-types SID --output katl_sids.html
```

### Example 2: Canadian Airports
```bash
# List Canadian airports
python3 arinc424_map_tool.py flyright.pc --region CAN --list-airports

# Show all Canadian airports and their procedures
python3 arinc424_map_tool.py flyright.pc --region CAN --show-all --output canada_aviation.html
```

### Example 3: Multiple US Airports
```bash
# Show procedures for major US airports
python3 arinc424_map_tool.py flyright.pc --icao KATL KLAX KORD KJFK --show-all --output major_us_airports.html
```

## Understanding the Output

### Map Features
- **Blue airplane icons**: Airports
- **Green circles**: Waypoints
- **Colored lines with arrows**: SID/STAR procedures
- **Popup information**: Click on any marker for detailed information
- **Layer control**: Toggle different layers on/off using the control in the top-right

### Procedure Visualization
- Each procedure is shown as a connected line between waypoints
- Arrows indicate the direction of flight
- Different procedures use different colors
- Waypoints are numbered in sequence order

### Data Statistics
The tool provides statistics about:
- Number of airports found
- Number of waypoints plotted
- Number of procedures visualized
- Breakdown by procedure type (SID/STAR/Approach)

## Troubleshooting

### Common Issues

1. **"Airport not found"**: Check that the ICAO code is correct and exists in the data
2. **"Insufficient waypoints"**: Some procedures may reference waypoints not in the database
3. **Empty map**: Ensure you've specified what to show (--show-airports, --show-procedures, etc.)

### Performance Notes
- Parsing large ARINC424 files can take 1-2 minutes
- Maps with many procedures may take time to render
- Consider filtering to specific airports for better performance

## File Structure

- `arinc424_parser.py`: Core parsing logic for ARINC424 data
- `map_visualizer.py`: Map creation and visualization functions
- `arinc424_map_tool.py`: Command-line interface
- `src/arinc424/`: Original ARINC424 library for record parsing

## Data Sources

This tool works with ARINC424 format navigation data files. The example file `flyright.pc` contains:
- 23 airports across USA, Canada, and South America
- 18,371 waypoints
- 2,813 procedures (SIDs, STARs, and approaches)

## Technical Details

### Coordinate System
- Uses WGS84 decimal degrees
- Automatically converts from ARINC424 format (degrees/minutes/seconds)

### Waypoint Resolution
The tool uses multiple strategies to find waypoint coordinates:
1. Exact match by identifier, ICAO code, and section
2. Partial matches with missing fields
3. Identifier-only search across all waypoints

### Procedure Types
- **SID (Standard Instrument Departure)**: Departure procedures from airports
- **STAR (Standard Terminal Arrival Route)**: Arrival procedures to airports
- **APPROACH**: Instrument approach procedures (if present in data)
