<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_1779a93c9a0dc60bd37ea7a7983c2861 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_1779a93c9a0dc60bd37ea7a7983c2861" ></div>
        
</body>
<script>
    
    
            var map_1779a93c9a0dc60bd37ea7a7983c2861 = L.map(
                "map_1779a93c9a0dc60bd37ea7a7983c2861",
                {
                    center: [33.6367, -84.4278],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 10,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_b425a8dda47a158f1fc12ed4bac37f24 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_b425a8dda47a158f1fc12ed4bac37f24.addTo(map_1779a93c9a0dc60bd37ea7a7983c2861);
        
    
            var tile_layer_d91991aca75abf5430a9f70e405941b6 = L.tileLayer(
                "https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 18,
  "maxNativeZoom": 18,
  "noWrap": false,
  "attribution": "Map tiles by Stamen Design, CC BY 3.0 \u2014 Map data \u00a9 OpenStreetMap contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_d91991aca75abf5430a9f70e405941b6.addTo(map_1779a93c9a0dc60bd37ea7a7983c2861);
        
    
            var tile_layer_a7b4803ffe652ce0542ddaec5ecb79d4 = L.tileLayer(
                "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
                {
  "minZoom": 0,
  "maxZoom": 20,
  "maxNativeZoom": 20,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors \u0026copy; \u003ca href=\"https://carto.com/attributions\"\u003eCARTO\u003c/a\u003e",
  "subdomains": "abcd",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_a7b4803ffe652ce0542ddaec5ecb79d4.addTo(map_1779a93c9a0dc60bd37ea7a7983c2861);
        
    
            var layer_control_92b362456c62433f6dd6376544fa705a_layers = {
                base_layers : {
                    "openstreetmap" : tile_layer_b425a8dda47a158f1fc12ed4bac37f24,
                    "Stamen Terrain" : tile_layer_d91991aca75abf5430a9f70e405941b6,
                    "CartoDB Positron" : tile_layer_a7b4803ffe652ce0542ddaec5ecb79d4,
                },
                overlays :  {
                    "Airports" : feature_group_9bbce467f3899b972c53c4f2feb25b5a,
                },
            };
            let layer_control_92b362456c62433f6dd6376544fa705a = L.control.layers(
                layer_control_92b362456c62433f6dd6376544fa705a_layers.base_layers,
                layer_control_92b362456c62433f6dd6376544fa705a_layers.overlays,
                {
  "position": "topright",
  "collapsed": true,
  "autoZIndex": true,
}
            ).addTo(map_1779a93c9a0dc60bd37ea7a7983c2861);

        
    
            var feature_group_9bbce467f3899b972c53c4f2feb25b5a = L.featureGroup(
                {
}
            );
        
    
            var marker_345cefa2918d989f396628fda5651f79 = L.marker(
                [33.6367, -842.9000861111111],
                {
}
            ).addTo(feature_group_9bbce467f3899b972c53c4f2feb25b5a);
        
    
            var icon_3e48abeb3453c7df86b0fb3cafdb035e = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_504d8dda5f94360b3347997d98ebecac = L.popup({
  "maxWidth": 300,
});

        
            
                var html_0e1a90cc9d4ec7ad60f8fe07b13997c3 = $(`<div id="html_0e1a90cc9d4ec7ad60f8fe07b13997c3" style="width: 100.0%; height: 100.0%;">             <b>HARTSFIELD - JACKSON ATLANTA I</b><br>             ICAO: KATL<br>             Region: USA<br>             Coordinates: 33.636700, -842.900086             </div>`)[0];
                popup_504d8dda5f94360b3347997d98ebecac.setContent(html_0e1a90cc9d4ec7ad60f8fe07b13997c3);
            
        

        marker_345cefa2918d989f396628fda5651f79.bindPopup(popup_504d8dda5f94360b3347997d98ebecac)
        ;

        
    
    
            marker_345cefa2918d989f396628fda5651f79.bindTooltip(
                `<div>
                     KATL - HARTSFIELD - JACKSON ATLANTA I
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_345cefa2918d989f396628fda5651f79.setIcon(icon_3e48abeb3453c7df86b0fb3cafdb035e);
            
    
            feature_group_9bbce467f3899b972c53c4f2feb25b5a.addTo(map_1779a93c9a0dc60bd37ea7a7983c2861);
        
</script>
</html>