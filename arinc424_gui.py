#!/usr/bin/env python3
"""
ARINC424 Map Tool - Graphical User Interface

A user-friendly GUI for parsing ARINC424 files and creating interactive maps.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
import webbrowser
from typing import List, Optional

from arinc424_parser import ARINC424Parser
from map_visualizer import MapVisualizer


class ARINC424GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ARINC424 Map Visualization Tool")
        self.root.geometry("800x700")
        
        # Data
        self.parser: Optional[ARINC424Parser] = None
        self.available_regions: List[str] = []
        self.available_airports: List[str] = []
        
        # Variables
        self.file_path = tk.StringVar()
        self.selected_region = tk.StringVar()
        self.selected_icaos = tk.StringVar()
        self.output_filename = tk.StringVar(value="arinc424_map.html")
        
        # Checkboxes
        self.show_airports = tk.BooleanVar(value=True)
        self.show_waypoints = tk.BooleanVar(value=False)
        self.show_procedures = tk.BooleanVar(value=True)
        self.show_sids = tk.BooleanVar(value=True)
        self.show_stars = tk.BooleanVar(value=True)
        self.show_approaches = tk.BooleanVar(value=False)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # File selection
        ttk.Label(main_frame, text="ARINC424 File:").grid(row=row, column=0, sticky=tk.W, pady=5)
        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=row, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        file_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(file_frame, text="Browse", command=self.browse_file).grid(row=0, column=1)
        ttk.Button(file_frame, text="Parse File", command=self.parse_file).grid(row=0, column=2, padx=(5, 0))
        
        row += 1
        
        # Separator
        ttk.Separator(main_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        row += 1
        
        # Filtering section
        filter_frame = ttk.LabelFrame(main_frame, text="Filtering Options", padding="10")
        filter_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        filter_frame.columnconfigure(1, weight=1)
        
        # Region selection
        ttk.Label(filter_frame, text="Region:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.region_combo = ttk.Combobox(filter_frame, textvariable=self.selected_region, state="readonly")
        self.region_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        self.region_combo.bind('<<ComboboxSelected>>', self.on_region_selected)
        
        # ICAO selection
        ttk.Label(filter_frame, text="ICAO Codes:").grid(row=1, column=0, sticky=tk.W, pady=2)
        icao_frame = ttk.Frame(filter_frame)
        icao_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        icao_frame.columnconfigure(0, weight=1)
        
        self.icao_entry = ttk.Entry(icao_frame, textvariable=self.selected_icaos)
        self.icao_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        # Add placeholder text manually
        self.icao_entry.insert(0, "e.g., KATL KORD KLAX")
        self.icao_entry.bind('<FocusIn>', self._clear_placeholder)
        self.icao_entry.bind('<FocusOut>', self._restore_placeholder)
        ttk.Button(icao_frame, text="List Airports", command=self.list_airports).grid(row=0, column=1)
        
        row += 1
        
        # Display options
        display_frame = ttk.LabelFrame(main_frame, text="Display Options", padding="10")
        display_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # Checkboxes in two columns
        ttk.Checkbutton(display_frame, text="Show Airports", variable=self.show_airports).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Checkbutton(display_frame, text="Show Waypoints", variable=self.show_waypoints).grid(row=0, column=1, sticky=tk.W)
        ttk.Checkbutton(display_frame, text="Show Procedures", variable=self.show_procedures).grid(row=1, column=0, sticky=tk.W, padx=(0, 20))
        
        # Procedure type checkboxes
        proc_frame = ttk.Frame(display_frame)
        proc_frame.grid(row=1, column=1, sticky=tk.W)
        ttk.Checkbutton(proc_frame, text="SIDs", variable=self.show_sids).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(proc_frame, text="STARs", variable=self.show_stars).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Checkbutton(proc_frame, text="Approaches", variable=self.show_approaches).grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        
        row += 1
        
        # Output options
        output_frame = ttk.LabelFrame(main_frame, text="Output Options", padding="10")
        output_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="Output File:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(output_frame, textvariable=self.output_filename).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        
        row += 1
        
        # Action buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="Generate Map", command=self.generate_map, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Open Map", command=self.open_map).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        
        row += 1
        
        # Log/Status area
        log_frame = ttk.LabelFrame(main_frame, text="Status Log", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(row, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Initial log message
        self.log("ARINC424 Map Visualization Tool initialized.")
        self.log("Please select an ARINC424 file to begin.")

        # Placeholder text state
        self.placeholder_active = True

    def _clear_placeholder(self, event):
        """Clear placeholder text when entry gets focus"""
        if self.placeholder_active:
            self.icao_entry.delete(0, tk.END)
            self.placeholder_active = False

    def _restore_placeholder(self, event):
        """Restore placeholder text if entry is empty"""
        if not self.selected_icaos.get():
            self.icao_entry.insert(0, "e.g., KATL KORD KLAX")
            self.placeholder_active = True
        
    def log(self, message: str):
        """Add a message to the log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """Clear the log"""
        self.log_text.delete(1.0, tk.END)
        
    def browse_file(self):
        """Browse for ARINC424 file"""
        filename = filedialog.askopenfilename(
            title="Select ARINC424 File",
            filetypes=[("PC files", "*.pc"), ("All files", "*.*")]
        )
        if filename:
            self.file_path.set(filename)
            
    def parse_file(self):
        """Parse the selected ARINC424 file"""
        if not self.file_path.get():
            messagebox.showerror("Error", "Please select an ARINC424 file first.")
            return
            
        if not os.path.exists(self.file_path.get()):
            messagebox.showerror("Error", "Selected file does not exist.")
            return
            
        # Disable UI during parsing
        self.set_ui_state(False)
        
        # Run parsing in a separate thread
        thread = threading.Thread(target=self._parse_file_thread)
        thread.daemon = True
        thread.start()
        
    def _parse_file_thread(self):
        """Parse file in a separate thread"""
        try:
            self.log(f"Parsing file: {self.file_path.get()}")
            self.parser = ARINC424Parser()
            
            # Override the print statements to log to GUI
            original_print = print
            def gui_print(*args, **kwargs):
                message = ' '.join(str(arg) for arg in args)
                self.root.after(0, lambda: self.log(message))
            
            # Temporarily replace print
            import builtins
            builtins.print = gui_print
            
            try:
                self.parser.parse_file(self.file_path.get())
            finally:
                # Restore original print
                builtins.print = original_print
            
            # Update UI with parsed data
            self.root.after(0, self._update_ui_after_parsing)
            
        except Exception as e:
            self.root.after(0, lambda: self.log(f"Error parsing file: {str(e)}"))
            self.root.after(0, lambda: self.set_ui_state(True))
            
    def _update_ui_after_parsing(self):
        """Update UI after successful parsing"""
        self.available_regions = self.parser.get_available_regions()
        self.region_combo['values'] = [''] + self.available_regions
        
        self.log("File parsed successfully!")
        self.log(f"Available regions: {', '.join(self.available_regions)}")
        
        self.set_ui_state(True)
        
    def set_ui_state(self, enabled: bool):
        """Enable or disable UI elements"""
        state = 'normal' if enabled else 'disabled'
        # This is a simplified version - in a full implementation,
        # you'd want to disable specific widgets
        
    def on_region_selected(self, event=None):
        """Handle region selection"""
        if self.parser and self.selected_region.get():
            airports = self.parser.get_airports_in_region(self.selected_region.get())
            self.log(f"Region {self.selected_region.get()} has {len(airports)} airports")
            
    def list_airports(self):
        """List airports based on current filters"""
        if not self.parser:
            messagebox.showwarning("Warning", "Please parse a file first.")
            return
            
        airports = []
        
        if self.selected_region.get():
            airports.extend(self.parser.get_airports_in_region(self.selected_region.get()))
            
        if self.selected_icaos.get():
            icao_list = self.selected_icaos.get().upper().split()
            for icao in icao_list:
                if icao in self.parser.airports:
                    airports.append(self.parser.airports[icao])
                    
        # Remove duplicates
        airports = list({airport.icao: airport for airport in airports}.values())
        
        if airports:
            self.log(f"Found {len(airports)} airports:")
            for airport in sorted(airports, key=lambda x: x.icao):
                self.log(f"  {airport.icao}: {airport.name}")
        else:
            self.log("No airports found matching the criteria.")
            
    def generate_map(self):
        """Generate the map"""
        if not self.parser:
            messagebox.showerror("Error", "Please parse a file first.")
            return
            
        # Validate inputs
        if not (self.show_airports.get() or self.show_waypoints.get() or self.show_procedures.get()):
            messagebox.showerror("Error", "Please select at least one display option.")
            return
            
        if not self.output_filename.get():
            messagebox.showerror("Error", "Please specify an output filename.")
            return
            
        # Disable UI during map generation
        self.set_ui_state(False)
        
        # Run map generation in a separate thread
        thread = threading.Thread(target=self._generate_map_thread)
        thread.daemon = True
        thread.start()
        
    def _generate_map_thread(self):
        """Generate map in a separate thread"""
        try:
            # Get target airports
            target_airports = []
            
            if self.selected_region.get():
                region_airports = self.parser.get_airports_in_region(self.selected_region.get())
                target_airports.extend(region_airports)
                self.root.after(0, lambda: self.log(f"Added {len(region_airports)} airports from region {self.selected_region.get()}"))
                
            if self.selected_icaos.get():
                icao_list = self.selected_icaos.get().upper().split()
                icao_airports = []
                for icao in icao_list:
                    if icao in self.parser.airports:
                        icao_airports.append(self.parser.airports[icao])
                    else:
                        self.root.after(0, lambda i=icao: self.log(f"Warning: Airport {i} not found"))
                target_airports.extend(icao_airports)
                
            # Remove duplicates
            target_airports = list({airport.icao: airport for airport in target_airports}.values())
            
            if not target_airports:
                self.root.after(0, lambda: self.log("Error: No airports found matching criteria"))
                self.root.after(0, lambda: self.set_ui_state(True))
                return
                
            self.root.after(0, lambda: self.log(f"Creating map for {len(target_airports)} airports..."))
            
            # Create visualizer
            visualizer = MapVisualizer(self.parser)
            
            # Calculate center point
            center_lat = sum(a.coordinate.latitude for a in target_airports) / len(target_airports)
            center_lon = sum(a.coordinate.longitude for a in target_airports) / len(target_airports)
            map_obj = visualizer.create_map(center_lat, center_lon, zoom_start=6)
            
            # Add airports
            if self.show_airports.get():
                visualizer.add_airports(target_airports)
                self.root.after(0, lambda: self.log(f"Added {len(target_airports)} airports to map"))
                
            # Add procedures
            procedures = []
            if self.show_procedures.get():
                procedure_types = []
                if self.show_sids.get():
                    procedure_types.append('SID')
                if self.show_stars.get():
                    procedure_types.append('STAR')
                if self.show_approaches.get():
                    procedure_types.append('APPROACH')
                    
                if procedure_types:
                    airport_icaos = [a.icao for a in target_airports]
                    procedures = self.parser.get_procedures_for_airports(airport_icaos, procedure_types)
                    visualizer.add_procedures(procedures)
                    self.root.after(0, lambda: self.log(f"Added {len(procedures)} procedures to map"))
                    
            # Add waypoints
            if self.show_waypoints.get():
                waypoint_ids = set()
                for proc in procedures:
                    for step in proc.steps:
                        waypoint_ids.add(step.waypoint_id)
                        
                relevant_waypoints = []
                for wp_key, waypoint in self.parser.waypoints.items():
                    if waypoint.identifier in waypoint_ids:
                        relevant_waypoints.append(waypoint)
                        
                visualizer.add_waypoints(relevant_waypoints)
                self.root.after(0, lambda: self.log(f"Added {len(relevant_waypoints)} waypoints to map"))
                
            # Fit map to data
            visualizer.fit_map_to_data(target_airports)
            
            # Add region summary if applicable
            if self.selected_region.get():
                visualizer.add_region_summary(self.selected_region.get())
                
            # Save map
            output_path = self.output_filename.get()
            visualizer.save_map(output_path)
            
            self.root.after(0, lambda: self.log(f"Map saved to {output_path}"))
            self.root.after(0, lambda: self.log("Map generation complete!"))
            
        except Exception as e:
            self.root.after(0, lambda: self.log(f"Error generating map: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.set_ui_state(True))
            
    def open_map(self):
        """Open the generated map in a web browser"""
        output_path = self.output_filename.get()
        if os.path.exists(output_path):
            webbrowser.open(f"file://{os.path.abspath(output_path)}")
            self.log(f"Opened {output_path} in web browser")
        else:
            messagebox.showerror("Error", f"Map file {output_path} not found. Please generate a map first.")


def main():
    root = tk.Tk()
    app = ARINC424GUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
