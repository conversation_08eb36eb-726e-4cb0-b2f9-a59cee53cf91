[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "arinc424"
version = "0.2.1"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
]
description = "A library for ARINC-424, the international standard file format for aircraft navigation data."
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
  "prettytable==3.9.0",
  "termcolor==2.3.0",
]

[project.urls]
"Homepage" = "https://github.com/jack-laverty/arinc424"
"Bug Tracker" = "https://github.com/jack-laverty/arinc424/issues"