#!/usr/bin/env python3
"""
Simple Map Visualizer for ARINC424 Data

Creates clean, reliable HTML maps using basic Leaflet.js without heavy dependencies.
"""

from typing import List, Dict, Optional, Tuple
import json
from arinc424_parser import ARINC424Parser, Airport, Waypoint, Procedure, Coordinate


class SimpleMapVisualizer:
    """Creates simple, reliable maps for ARINC424 data"""
    
    def __init__(self, parser: ARINC424Parser):
        self.parser = parser
        
    def create_map(self, airports: List[Airport], procedures: List[Procedure] = None, 
                   waypoints: List[Waypoint] = None, output_file: str = "arinc424_map.html") -> None:
        """Create a simple HTML map with the given data"""
        
        # Calculate map center and bounds
        if airports:
            center_lat = sum(a.coordinate.latitude for a in airports) / len(airports)
            center_lon = sum(a.coordinate.longitude for a in airports) / len(airports)
        else:
            center_lat, center_lon = 39.8283, -98.5795  # US center
            
        # Determine zoom level based on data spread
        if len(airports) > 1:
            lats = [a.coordinate.latitude for a in airports]
            lons = [a.coordinate.longitude for a in airports]
            lat_range = max(lats) - min(lats)
            lon_range = max(lons) - min(lons)
            max_range = max(lat_range, lon_range)
            
            if max_range > 20:
                zoom = 4
            elif max_range > 10:
                zoom = 5
            elif max_range > 5:
                zoom = 6
            elif max_range > 2:
                zoom = 7
            else:
                zoom = 8
        else:
            zoom = 10
            
        # Prepare data for JavaScript
        airports_data = []
        for airport in airports:
            airports_data.append({
                'icao': airport.icao,
                'name': airport.name,
                'lat': airport.coordinate.latitude,
                'lon': airport.coordinate.longitude,
                'region': airport.region
            })
            
        waypoints_data = []
        if waypoints:
            for waypoint in waypoints:
                waypoints_data.append({
                    'id': waypoint.identifier,
                    'lat': waypoint.coordinate.latitude,
                    'lon': waypoint.coordinate.longitude,
                    'type': waypoint.waypoint_type,
                    'region': waypoint.region,
                    'icao': waypoint.icao_code
                })
                
        procedures_data = []
        if procedures:
            for procedure in procedures:
                waypoint_coords = self.parser.resolve_waypoint_coordinates(procedure)
                valid_coords = [(name, coord) for name, coord in waypoint_coords if coord is not None]
                
                if len(valid_coords) >= 2:
                    proc_data = {
                        'id': procedure.procedure_id,
                        'type': procedure.procedure_type,
                        'airport': procedure.airport_icao,
                        'transition': procedure.transition,
                        'waypoints': []
                    }
                    
                    for i, (name, coord) in enumerate(valid_coords):
                        proc_data['waypoints'].append({
                            'name': name,
                            'lat': coord.latitude,
                            'lon': coord.longitude,
                            'sequence': i + 1
                        })
                    
                    procedures_data.append(proc_data)
        
        # Generate HTML
        html_content = self._generate_html(
            center_lat, center_lon, zoom,
            airports_data, waypoints_data, procedures_data
        )
        
        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        print(f"Simple map saved to {output_file}")
        
    def _generate_html(self, center_lat: float, center_lon: float, zoom: int,
                      airports: List[Dict], waypoints: List[Dict], procedures: List[Dict]) -> str:
        """Generate the HTML content for the map"""
        
        # Convert data to JSON for JavaScript
        airports_json = json.dumps(airports, indent=2)
        waypoints_json = json.dumps(waypoints, indent=2)
        procedures_json = json.dumps(procedures, indent=2)
        
        html = f"""<!DOCTYPE html>
<html>
<head>
    <title>ARINC424 Navigation Map</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>
    
    <style>
        #map {{ 
            height: 100vh; 
            width: 100%;
        }}
        body {{
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }}
        .info-panel {{
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 250px;
        }}
        .legend {{
            margin-top: 10px;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            margin: 5px 0;
        }}
        .legend-icon {{
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border-radius: 3px;
        }}
        .airport-icon {{ background: #2196F3; }}
        .waypoint-icon {{ background: #4CAF50; }}
        .sid-icon {{ background: #FF5722; }}
        .star-icon {{ background: #9C27B0; }}
    </style>
</head>
<body>
    <div class="info-panel">
        <h3>ARINC424 Map</h3>
        <p><strong>Airports:</strong> {len(airports)}</p>
        <p><strong>Waypoints:</strong> {len(waypoints)}</p>
        <p><strong>Procedures:</strong> {len(procedures)}</p>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-icon airport-icon"></div>
                <span>Airports</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon waypoint-icon"></div>
                <span>Waypoints</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon sid-icon"></div>
                <span>SIDs</span>
            </div>
            <div class="legend-item">
                <div class="legend-icon star-icon"></div>
                <span>STARs</span>
            </div>
        </div>
    </div>
    
    <div id="map"></div>
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
     integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
     crossorigin=""></script>
    
    <script>
        // Data
        const airports = {airports_json};
        const waypoints = {waypoints_json};
        const procedures = {procedures_json};
        
        console.log('Loading map with:', airports.length, 'airports,', waypoints.length, 'waypoints,', procedures.length, 'procedures');
        
        // Initialize map
        const map = L.map('map').setView([{center_lat}, {center_lon}], {zoom});
        
        // Add tile layer
        L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png', {{
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }}).addTo(map);
        
        // Add airports
        airports.forEach(function(airport) {{
            const marker = L.marker([airport.lat, airport.lon], {{
                title: airport.icao + ' - ' + airport.name
            }}).addTo(map);
            
            marker.bindPopup(`
                <div>
                    <h4>${{airport.icao}} - ${{airport.name}}</h4>
                    <p><strong>Region:</strong> ${{airport.region}}</p>
                    <p><strong>Coordinates:</strong> ${{airport.lat.toFixed(6)}}°, ${{airport.lon.toFixed(6)}}°</p>
                </div>
            `);
        }});
        
        // Add waypoints
        waypoints.forEach(function(waypoint) {{
            const marker = L.circleMarker([waypoint.lat, waypoint.lon], {{
                radius: 4,
                fillColor: '#4CAF50',
                color: '#2E7D32',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            }}).addTo(map);
            
            marker.bindPopup(`
                <div>
                    <h4>${{waypoint.id}}</h4>
                    <p><strong>Type:</strong> ${{waypoint.type}}</p>
                    <p><strong>Region:</strong> ${{waypoint.region}}</p>
                    <p><strong>ICAO:</strong> ${{waypoint.icao}}</p>
                    <p><strong>Coordinates:</strong> ${{waypoint.lat.toFixed(6)}}°, ${{waypoint.lon.toFixed(6)}}°</p>
                </div>
            `);
        }});
        
        // Add procedures
        const colors = ['#FF5722', '#9C27B0', '#3F51B5', '#009688', '#FF9800', '#795548', '#607D8B'];
        let colorIndex = 0;
        
        procedures.forEach(function(procedure) {{
            const color = procedure.type === 'SID' ? '#FF5722' : '#9C27B0';
            const coords = procedure.waypoints.map(wp => [wp.lat, wp.lon]);
            
            // Draw the procedure line
            const line = L.polyline(coords, {{
                color: color,
                weight: 3,
                opacity: 0.8
            }}).addTo(map);
            
            line.bindPopup(`
                <div>
                    <h4>${{procedure.type}}: ${{procedure.id}}</h4>
                    <p><strong>Airport:</strong> ${{procedure.airport}}</p>
                    <p><strong>Transition:</strong> ${{procedure.transition || 'None'}}</p>
                    <p><strong>Waypoints:</strong> ${{procedure.waypoints.length}}</p>
                </div>
            `);
            
            // Add waypoint markers for the procedure
            procedure.waypoints.forEach(function(wp, index) {{
                const marker = L.circleMarker([wp.lat, wp.lon], {{
                    radius: 6,
                    fillColor: color,
                    color: 'white',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.9
                }}).addTo(map);
                
                marker.bindPopup(`
                    <div>
                        <h4>${{wp.name}}</h4>
                        <p><strong>Procedure:</strong> ${{procedure.type}} ${{procedure.id}}</p>
                        <p><strong>Sequence:</strong> ${{wp.sequence}}</p>
                        <p><strong>Coordinates:</strong> ${{wp.lat.toFixed(6)}}°, ${{wp.lon.toFixed(6)}}°</p>
                    </div>
                `);
                
                // Add sequence number
                if (index < procedure.waypoints.length - 1) {{
                    const midLat = (wp.lat + procedure.waypoints[index + 1].lat) / 2;
                    const midLon = (wp.lon + procedure.waypoints[index + 1].lon) / 2;
                    
                    L.marker([midLat, midLon], {{
                        icon: L.divIcon({{
                            html: '<div style="background: white; border: 1px solid ' + color + '; border-radius: 50%; width: 20px; height: 20px; text-align: center; line-height: 18px; font-size: 12px; font-weight: bold;">' + (index + 1) + '</div>',
                            iconSize: [20, 20],
                            iconAnchor: [10, 10]
                        }})
                    }}).addTo(map);
                }}
            }});
        }});
        
        console.log('Map loaded successfully');
    </script>
</body>
</html>"""
        
        return html
