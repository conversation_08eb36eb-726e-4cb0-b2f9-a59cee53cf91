# ARINC424 Map Tool - Graphical User Interface

This document describes the GUI versions of the ARINC424 Map Tool, which provide user-friendly interfaces for parsing ARINC424 files and creating interactive maps.

## Available GUI Versions

### 1. Simple GUI (`arinc424_simple_gui.py`)
**Recommended for most users**
- Clean, straightforward interface
- Synchronous processing (no threading complexity)
- All essential features
- Better error handling and user feedback

### 2. Advanced GUI (`arinc424_gui.py`)
**For advanced users**
- More detailed options
- Asynchronous processing with progress logging
- Threaded operations for better responsiveness
- Comprehensive status logging

## Installation and Requirements

### Prerequisites
- Python 3.10 or higher
- tkinter (usually included with Python)
- folium library for map generation

### Installation
```bash
# Install required dependencies
pip install folium

# No additional installation needed - just run the GUI scripts
```

## Quick Start Guide

### Using the Simple GUI

1. **Launch the application:**
   ```bash
   python3 arinc424_simple_gui.py
   ```

2. **Select your ARINC424 file:**
   - Click "Browse" to select your .pc file (e.g., flyright.pc)
   - Click "Parse File" and wait for processing to complete

3. **Choose airports to visualize:**
   - **By Region:** Select from dropdown (USA, CAN, SAM)
   - **By ICAO:** Enter airport codes separated by spaces (e.g., KATL KORD KLAX)
   - **Both:** You can use both methods together

4. **Select what to display:**
   - ☑️ Show Airports: Display airport locations
   - ☑️ Show Procedures: Display SID/STAR routes
   - ☑️ SIDs: Include Standard Instrument Departures
   - ☑️ STARs: Include Standard Terminal Arrival Routes

5. **Generate and view the map:**
   - Specify output filename (default: arinc424_map.html)
   - Click "Generate Map"
   - Click "Open Map" to view in your web browser

## Detailed Features

### File Parsing
- **Supported formats:** ARINC424 .pc files
- **Processing time:** 1-2 minutes for large files
- **Data extracted:**
  - Airports with coordinates and names
  - Waypoints with types and locations
  - SID/STAR procedures with waypoint sequences

### Filtering Options

#### Region-Based Filtering
Available regions in the sample data:
- **USA:** 14 airports (KATL, KLAX, KORD, etc.)
- **CAN:** 3 airports (Canadian airports)
- **SAM:** 6 airports (South American airports)

#### ICAO-Based Filtering
- Enter multiple airport codes separated by spaces
- Case-insensitive (katl = KATL)
- Invalid codes are ignored with warnings

### Display Options

#### Airports
- Blue airplane icons on the map
- Clickable markers with detailed information:
  - Airport name and ICAO code
  - Region and coordinates
  - Associated procedures

#### Procedures (SIDs/STARs)
- Colored lines connecting waypoints in sequence
- Different colors for different procedures
- Directional arrows showing flight path
- Clickable waypoints with sequence information

### Map Features

#### Interactive Elements
- **Zoom and pan:** Mouse wheel and drag
- **Layer control:** Toggle different data layers
- **Multiple tile layers:** Street map, terrain, satellite
- **Popup information:** Click any marker for details

#### Automatic Features
- **Smart centering:** Map automatically centers on selected airports
- **Optimal zoom:** Adjusts zoom level to fit all data
- **Region summaries:** Statistics box for regional views

## Usage Examples

### Example 1: Explore Atlanta Airport
1. Parse your ARINC424 file
2. Enter "KATL" in the ICAO field
3. Check "Show Airports" and "Show Procedures"
4. Generate map to see all Atlanta SIDs and STARs

### Example 2: Regional Overview
1. Parse your ARINC424 file
2. Select "USA" from the Region dropdown
3. Check "Show Airports" only for a clean overview
4. Generate map to see all US airports

### Example 3: Compare Major Airports
1. Parse your ARINC424 file
2. Enter "KATL KLAX KORD KJFK" in the ICAO field
3. Check "Show Procedures" and select "STARs" only
4. Generate map to compare arrival procedures

### Example 4: Canadian Aviation
1. Parse your ARINC424 file
2. Select "CAN" from the Region dropdown
3. Check all display options
4. Generate comprehensive Canadian aviation map

## Troubleshooting

### Common Issues

#### "No airports found matching criteria"
- **Cause:** Invalid ICAO codes or empty region
- **Solution:** Use "List Airports" to see available options

#### "Please parse a file first"
- **Cause:** Trying to generate map before parsing data
- **Solution:** Select and parse an ARINC424 file first

#### "Procedure has insufficient waypoints"
- **Cause:** Some procedures reference waypoints not in the database
- **Effect:** Those procedures won't be displayed (this is normal)

#### Map appears empty
- **Cause:** No display options selected
- **Solution:** Check at least one of "Show Airports" or "Show Procedures"

### Performance Tips

#### For Large Files
- Parsing may take 1-2 minutes - be patient
- Consider filtering to specific airports for faster map generation
- Close other applications if memory is limited

#### For Better Maps
- Use region filtering for cleaner regional views
- Combine airports and procedures for comprehensive visualization
- Try different procedure types (SIDs vs STARs) separately

## File Outputs

### Generated Maps
- **Format:** HTML files that open in any web browser
- **Interactivity:** Fully interactive with zoom, pan, and clickable elements
- **Sharing:** Can be shared via email or web hosting
- **Offline:** Work without internet connection once generated

### Map Contents
- **Base layers:** Multiple map styles (street, terrain, satellite)
- **Data layers:** Separate layers for airports, waypoints, procedures
- **Controls:** Layer toggle, zoom controls, scale indicator
- **Information:** Popup details for all map elements

## Technical Details

### Data Processing
- **Coordinate system:** WGS84 decimal degrees
- **Waypoint resolution:** Multiple fallback strategies for finding coordinates
- **Procedure parsing:** Extracts sequence, waypoints, and routing information

### Map Technology
- **Library:** Folium (Python wrapper for Leaflet.js)
- **Tiles:** OpenStreetMap, Stamen Terrain, CartoDB
- **Markers:** Custom icons for airports, circles for waypoints
- **Lines:** Colored polylines with directional arrows

### GUI Framework
- **Library:** tkinter (Python standard library)
- **Compatibility:** Works on Windows, macOS, and Linux
- **Threading:** Advanced GUI uses threading for responsiveness

## Comparison with Command Line Tool

| Feature | GUI | Command Line |
|---------|-----|--------------|
| Ease of use | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| Batch processing | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| Scripting | ⭐ | ⭐⭐⭐⭐⭐ |
| Visual feedback | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| Learning curve | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## Support and Development

### Getting Help
- Check this README for common solutions
- Review error messages carefully
- Ensure all dependencies are installed

### Contributing
- The GUI is built on top of the core parsing library
- Modifications can be made to improve user experience
- Additional features can be added to the visualization

### Future Enhancements
Potential improvements:
- Real-time progress bars
- Map export to different formats
- Advanced filtering options
- Procedure animation
- 3D visualization options
