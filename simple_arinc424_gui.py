#!/usr/bin/env python3
"""
Simple ARINC424 GUI - Reliable Version

Uses the simple map visualizer for guaranteed compatibility.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import webbrowser
from typing import List, Optional

from arinc424_parser import <PERSON><PERSON><PERSON>424Parser
from simple_map_visualizer import SimpleMapVisualizer


class SimpleARINC424GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ARINC424 Map Tool - Simple & Reliable")
        self.root.geometry("600x550")
        
        # Data
        self.parser: Optional[ARINC424Parser] = None
        self.available_regions: List[str] = []
        
        # Variables
        self.file_path = tk.StringVar()
        self.selected_region = tk.StringVar()
        self.selected_icaos = tk.StringVar()
        self.output_filename = tk.StringVar(value="arinc424_simple_map.html")
        
        # Options
        self.show_airports = tk.BooleanVar(value=True)
        self.show_procedures = tk.BooleanVar(value=True)
        self.show_waypoints = tk.BooleanVar(value=False)
        self.show_sids = tk.BooleanVar(value=True)
        self.show_stars = tk.BooleanVar(value=True)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame with padding
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="ARINC424 Map Tool - Simple & Reliable", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # File selection
        file_frame = ttk.LabelFrame(main_frame, text="1. Select ARINC424 File", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 15))
        
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X)
        
        ttk.Entry(file_entry_frame, textvariable=self.file_path, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="Browse", command=self.browse_file).pack(side=tk.RIGHT, padx=(10, 0))
        
        ttk.Button(file_frame, text="Parse File", command=self.parse_file).pack(pady=(10, 0))
        
        # Filtering
        filter_frame = ttk.LabelFrame(main_frame, text="2. Choose Airports", padding="10")
        filter_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Region selection
        region_frame = ttk.Frame(filter_frame)
        region_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(region_frame, text="By Region:").pack(side=tk.LEFT)
        self.region_combo = ttk.Combobox(region_frame, textvariable=self.selected_region, 
                                        state="readonly", width=15)
        self.region_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # ICAO selection
        icao_frame = ttk.Frame(filter_frame)
        icao_frame.pack(fill=tk.X)
        ttk.Label(icao_frame, text="By ICAO:").pack(side=tk.LEFT)
        icao_entry = ttk.Entry(icao_frame, textvariable=self.selected_icaos, width=30)
        icao_entry.pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(icao_frame, text="(e.g., KATL KORD KLAX)", foreground="gray").pack(side=tk.LEFT, padx=(5, 0))
        
        # Display options
        display_frame = ttk.LabelFrame(main_frame, text="3. Display Options", padding="10")
        display_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Checkbutton(display_frame, text="Show Airports", variable=self.show_airports).pack(anchor=tk.W)
        ttk.Checkbutton(display_frame, text="Show Procedures", variable=self.show_procedures).pack(anchor=tk.W)
        ttk.Checkbutton(display_frame, text="Show Waypoints", variable=self.show_waypoints).pack(anchor=tk.W)
        
        proc_frame = ttk.Frame(display_frame)
        proc_frame.pack(fill=tk.X, padx=(20, 0))
        ttk.Checkbutton(proc_frame, text="SIDs", variable=self.show_sids).pack(side=tk.LEFT)
        ttk.Checkbutton(proc_frame, text="STARs", variable=self.show_stars).pack(side=tk.LEFT, padx=(20, 0))
        
        # Output
        output_frame = ttk.LabelFrame(main_frame, text="4. Output", padding="10")
        output_frame.pack(fill=tk.X, pady=(0, 15))
        
        output_entry_frame = ttk.Frame(output_frame)
        output_entry_frame.pack(fill=tk.X)
        ttk.Label(output_entry_frame, text="Filename:").pack(side=tk.LEFT)
        ttk.Entry(output_entry_frame, textvariable=self.output_filename, width=30).pack(side=tk.LEFT, padx=(10, 0))
        
        # Action buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="Generate Simple Map", command=self.generate_map,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Open Map", command=self.open_map).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="List Airports", command=self.list_airports).pack(side=tk.LEFT)
        
        # Status
        self.status_var = tk.StringVar(value="Ready. Please select an ARINC424 file.")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        status_label.pack(pady=(20, 0))
        
        # Info label
        info_label = ttk.Label(main_frame, 
                              text="This version creates simple, reliable maps that work in all browsers.",
                              foreground="green", font=('Arial', 9))
        info_label.pack(pady=(5, 0))
        
    def browse_file(self):
        """Browse for ARINC424 file"""
        filename = filedialog.askopenfilename(
            title="Select ARINC424 File",
            filetypes=[("PC files", "*.pc"), ("All files", "*.*")]
        )
        if filename:
            self.file_path.set(filename)
            
    def parse_file(self):
        """Parse the selected ARINC424 file"""
        if not self.file_path.get():
            messagebox.showerror("Error", "Please select an ARINC424 file first.")
            return
            
        if not os.path.exists(self.file_path.get()):
            messagebox.showerror("Error", "Selected file does not exist.")
            return
            
        try:
            self.status_var.set("Parsing file... This may take a minute.")
            self.root.update()
            
            self.parser = ARINC424Parser()
            self.parser.parse_file(self.file_path.get())
            
            # Update region dropdown
            self.available_regions = self.parser.get_available_regions()
            self.region_combo['values'] = [''] + self.available_regions
            
            self.status_var.set(f"File parsed! Found {len(self.parser.airports)} airports, "
                              f"{len(self.parser.waypoints)} waypoints, {len(self.parser.procedures)} procedures")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to parse file: {str(e)}")
            self.status_var.set("Error parsing file.")
            
    def list_airports(self):
        """List airports based on current filters"""
        if not self.parser:
            messagebox.showwarning("Warning", "Please parse a file first.")
            return
            
        airports = []
        
        if self.selected_region.get():
            airports.extend(self.parser.get_airports_in_region(self.selected_region.get()))
            
        if self.selected_icaos.get():
            icao_list = self.selected_icaos.get().upper().split()
            for icao in icao_list:
                if icao in self.parser.airports:
                    airports.append(self.parser.airports[icao])
                    
        # Remove duplicates
        airports = list({airport.icao: airport for airport in airports}.values())
        
        if airports:
            airport_list = "\n".join([f"{a.icao}: {a.name}" for a in sorted(airports, key=lambda x: x.icao)])
            messagebox.showinfo("Airports Found", f"Found {len(airports)} airports:\n\n{airport_list}")
        else:
            messagebox.showinfo("No Airports", "No airports found matching the criteria.")
            
    def generate_map(self):
        """Generate the simple map"""
        if not self.parser:
            messagebox.showerror("Error", "Please parse a file first.")
            return
            
        if not (self.show_airports.get() or self.show_procedures.get() or self.show_waypoints.get()):
            messagebox.showerror("Error", "Please select at least one display option.")
            return
            
        if not self.output_filename.get():
            messagebox.showerror("Error", "Please specify an output filename.")
            return
            
        try:
            self.status_var.set("Generating simple map...")
            self.root.update()
            
            # Get target airports
            target_airports = []
            
            if self.selected_region.get():
                region_airports = self.parser.get_airports_in_region(self.selected_region.get())
                target_airports.extend(region_airports)
                
            if self.selected_icaos.get():
                icao_list = self.selected_icaos.get().upper().split()
                for icao in icao_list:
                    if icao in self.parser.airports:
                        target_airports.append(self.parser.airports[icao])
                        
            # Remove duplicates
            target_airports = list({airport.icao: airport for airport in target_airports}.values())
            
            if not target_airports:
                messagebox.showerror("Error", "No airports found matching criteria.")
                return
                
            # Get procedures
            procedures = []
            if self.show_procedures.get():
                procedure_types = []
                if self.show_sids.get():
                    procedure_types.append('SID')
                if self.show_stars.get():
                    procedure_types.append('STAR')
                    
                if procedure_types:
                    airport_icaos = [a.icao for a in target_airports]
                    procedures = self.parser.get_procedures_for_airports(airport_icaos, procedure_types)
                    
            # Get waypoints
            waypoints = []
            if self.show_waypoints.get():
                waypoint_ids = set()
                for proc in procedures:
                    for step in proc.steps:
                        waypoint_ids.add(step.waypoint_id)
                        
                for wp_key, waypoint in self.parser.waypoints.items():
                    if waypoint.identifier in waypoint_ids:
                        waypoints.append(waypoint)
                        
            # Create visualizer and generate map
            visualizer = SimpleMapVisualizer(self.parser)
            
            airports_to_show = target_airports if self.show_airports.get() else []
            procedures_to_show = procedures if self.show_procedures.get() else []
            waypoints_to_show = waypoints if self.show_waypoints.get() else []
            
            visualizer.create_map(
                airports=airports_to_show,
                procedures=procedures_to_show,
                waypoints=waypoints_to_show,
                output_file=self.output_filename.get()
            )
            
            self.status_var.set(f"Simple map generated! {len(airports_to_show)} airports, "
                              f"{len(procedures_to_show)} procedures, {len(waypoints_to_show)} waypoints")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate map: {str(e)}")
            self.status_var.set("Error generating map.")
            
    def open_map(self):
        """Open the generated map in a web browser"""
        output_path = self.output_filename.get()
        if os.path.exists(output_path):
            webbrowser.open(f"file://{os.path.abspath(output_path)}")
            self.status_var.set(f"Opened {output_path} in web browser")
        else:
            messagebox.showerror("Error", f"Map file {output_path} not found. Please generate a map first.")


def main():
    root = tk.Tk()
    app = SimpleARINC424GUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
