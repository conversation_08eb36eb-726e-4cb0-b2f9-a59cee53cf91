<!DOCTYPE html>
<html>
<head>
    <title>Simple Map Test</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>
    
    <style>
        #map { 
            height: 600px; 
            width: 100%;
        }
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .info {
            background: white;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>ARINC424 Map Test</h2>
        <p>Testing basic map functionality with Atlanta airport (KATL)</p>
        <p>Expected location: Atlanta, Georgia (33.6367°N, 84.4278°W)</p>
    </div>
    
    <div id="map"></div>
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
     integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
     crossorigin=""></script>
    
    <script>
        console.log('Starting map initialization...');
        
        // Initialize the map
        var map = L.map('map').setView([33.6367, -84.4278], 10);
        
        console.log('Map object created');
        
        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(map);
        
        console.log('Tile layer added');
        
        // Add Atlanta airport marker
        var atlMarker = L.marker([33.6367, -84.4278])
            .addTo(map)
            .bindPopup('<b>KATL - Atlanta Hartsfield-Jackson</b><br>Coordinates: 33.6367°N, 84.4278°W')
            .openPopup();
        
        console.log('Atlanta marker added');
        
        // Add some test waypoints around Atlanta
        var waypoints = [
            {name: 'ONDRE', lat: 34.1941, lon: -83.9736},
            {name: 'Test Point 1', lat: 33.7, lon: -84.3},
            {name: 'Test Point 2', lat: 33.5, lon: -84.5}
        ];
        
        waypoints.forEach(function(wp) {
            L.circleMarker([wp.lat, wp.lon], {
                radius: 6,
                fillColor: 'green',
                color: 'darkgreen',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            }).addTo(map).bindPopup('<b>' + wp.name + '</b><br>Lat: ' + wp.lat + '<br>Lon: ' + wp.lon);
        });
        
        console.log('Waypoints added');
        
        // Add a test line
        var testLine = L.polyline([
            [33.6367, -84.4278],
            [34.1941, -83.9736]
        ], {color: 'red', weight: 3}).addTo(map);
        
        console.log('Test line added');
        
        // Log map bounds
        setTimeout(function() {
            console.log('Map bounds:', map.getBounds());
            console.log('Map center:', map.getCenter());
            console.log('Map zoom:', map.getZoom());
        }, 1000);
    </script>
</body>
</html>
