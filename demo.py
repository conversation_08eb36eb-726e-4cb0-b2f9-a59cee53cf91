#!/usr/bin/env python3
"""
Demo script showing the capabilities of the ARINC424 Map Tool
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and show its output"""
    print(f"\n{'='*60}")
    print(f"DEMO: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=180)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Command timed out after 3 minutes")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    print("ARINC424 Map Tool Demonstration")
    print("This demo shows the key features of the tool")
    
    # Check if the data file exists
    if not os.path.exists("flyright.pc"):
        print("Error: flyright.pc not found. Please run this demo from the correct directory.")
        sys.exit(1)
    
    # Demo 1: List available regions
    run_command(
        "python3 arinc424_map_tool.py flyright.pc --list-regions",
        "List all available regions in the dataset"
    )
    
    # Demo 2: List airports in USA region
    run_command(
        "python3 arinc424_map_tool.py flyright.pc --region USA --list-airports",
        "List all airports in the USA region"
    )
    
    # Demo 3: Create a map for Atlanta airport with all procedures
    run_command(
        "python3 arinc424_map_tool.py flyright.pc --icao KATL --show-all --output demo_katl.html",
        "Create interactive map for Atlanta (KATL) showing airport, waypoints, and all procedures"
    )
    
    # Demo 4: Create a map showing only SIDs for Seattle
    run_command(
        "python3 arinc424_map_tool.py flyright.pc --icao KSEA --show-airports --show-procedures --procedure-types SID --output demo_ksea_sids.html",
        "Create map for Seattle (KSEA) showing only SID procedures"
    )
    
    # Demo 5: Create a regional map for Canada
    run_command(
        "python3 arinc424_map_tool.py flyright.pc --region CAN --show-airports --show-procedures --output demo_canada.html",
        "Create map for all Canadian airports and their procedures"
    )
    
    # Demo 6: Show multiple major US airports
    run_command(
        "python3 arinc424_map_tool.py flyright.pc --icao KATL KLAX KORD --show-airports --show-procedures --procedure-types STAR --output demo_major_stars.html",
        "Create map showing STAR procedures for major US airports (Atlanta, Los Angeles, Chicago)"
    )
    
    print(f"\n{'='*60}")
    print("DEMO COMPLETE!")
    print("Generated map files:")
    print("  - demo_katl.html: Atlanta airport with all procedures")
    print("  - demo_ksea_sids.html: Seattle SID procedures")
    print("  - demo_canada.html: Canadian airports and procedures")
    print("  - demo_major_stars.html: STAR procedures for major US airports")
    print("\nOpen these HTML files in a web browser to view the interactive maps.")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
