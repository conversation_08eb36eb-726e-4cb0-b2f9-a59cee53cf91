#!/usr/bin/env python3
"""
Map Visualization Module for ARINC424 Data

This module creates interactive maps showing airports, waypoints, and procedures.
"""

import folium
from folium import plugins
from typing import List, Dict, Optional, Tuple
import json
from arinc424_parser import ARINC424Parser, Airport, Waypoint, Procedure, Coordinate


class MapVisualizer:
    """Creates interactive maps for ARINC424 data"""
    
    def __init__(self, parser: ARINC424Parser):
        self.parser = parser
        self.map = None
        
    def create_map(self, center_lat: float = 39.8283, center_lon: float = -98.5795, 
                   zoom_start: int = 4) -> folium.Map:
        """Create a base map centered on the specified coordinates"""
        self.map = folium.Map(
            location=[center_lat, center_lon],
            zoom_start=zoom_start,
            tiles='OpenStreetMap'
        )
        
        # Add additional tile layers
        folium.TileLayer(
            tiles='https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png',
            attr='Map tiles by Stamen Design, CC BY 3.0 — Map data © OpenStreetMap contributors',
            name='Stamen Terrain'
        ).add_to(self.map)
        folium.TileLayer(
            tiles='CartoDB positron',
            name='CartoDB Positron'
        ).add_to(self.map)
        
        # Add layer control
        folium.LayerControl().add_to(self.map)
        
        return self.map
        
    def add_airports(self, airports: List[Airport], layer_name: str = "Airports") -> None:
        """Add airports to the map"""
        if not self.map:
            raise ValueError("Map not initialized. Call create_map() first.")
            
        airport_group = folium.FeatureGroup(name=layer_name)
        
        for airport in airports:
            popup_text = f"""
            <b>{airport.name}</b><br>
            ICAO: {airport.icao}<br>
            Region: {airport.region}<br>
            Coordinates: {airport.coordinate.latitude:.6f}, {airport.coordinate.longitude:.6f}
            """
            
            folium.Marker(
                location=[airport.coordinate.latitude, airport.coordinate.longitude],
                popup=folium.Popup(popup_text, max_width=300),
                tooltip=f"{airport.icao} - {airport.name}",
                icon=folium.Icon(color='blue', icon='plane', prefix='fa')
            ).add_to(airport_group)
            
        airport_group.add_to(self.map)
        
    def add_waypoints(self, waypoints: List[Waypoint], layer_name: str = "Waypoints") -> None:
        """Add waypoints to the map"""
        if not self.map:
            raise ValueError("Map not initialized. Call create_map() first.")
            
        waypoint_group = folium.FeatureGroup(name=layer_name)
        
        for waypoint in waypoints:
            popup_text = f"""
            <b>{waypoint.identifier}</b><br>
            Type: {waypoint.waypoint_type}<br>
            Region: {waypoint.region}<br>
            ICAO: {waypoint.icao_code}<br>
            Coordinates: {waypoint.coordinate.latitude:.6f}, {waypoint.coordinate.longitude:.6f}
            """
            
            folium.CircleMarker(
                location=[waypoint.coordinate.latitude, waypoint.coordinate.longitude],
                radius=4,
                popup=folium.Popup(popup_text, max_width=300),
                tooltip=waypoint.identifier,
                color='green',
                fillColor='lightgreen',
                fillOpacity=0.7
            ).add_to(waypoint_group)
            
        waypoint_group.add_to(self.map)
        
    def add_procedure(self, procedure: Procedure, color: str = 'red') -> None:
        """Add a single procedure (SID/STAR) to the map"""
        if not self.map:
            raise ValueError("Map not initialized. Call create_map() first.")
            
        # Resolve waypoint coordinates
        waypoint_coords = self.parser.resolve_waypoint_coordinates(procedure)
        
        # Filter out waypoints without coordinates
        valid_coords = [(name, coord) for name, coord in waypoint_coords if coord is not None]
        
        if len(valid_coords) < 2:
            print(f"Warning: Procedure {procedure.procedure_id} has insufficient waypoints with coordinates")
            return
            
        # Create the procedure layer
        layer_name = f"{procedure.procedure_type}: {procedure.procedure_id}"
        if procedure.transition:
            layer_name += f" ({procedure.transition})"
            
        procedure_group = folium.FeatureGroup(name=layer_name)
        
        # Add waypoint markers
        for i, (waypoint_name, coord) in enumerate(valid_coords):
            popup_text = f"""
            <b>{waypoint_name}</b><br>
            Procedure: {procedure.procedure_id}<br>
            Type: {procedure.procedure_type}<br>
            Sequence: {i+1}<br>
            Airport: {procedure.airport_icao}
            """
            
            folium.CircleMarker(
                location=[coord.latitude, coord.longitude],
                radius=6,
                popup=folium.Popup(popup_text, max_width=300),
                tooltip=f"{waypoint_name} ({i+1})",
                color=color,
                fillColor=color,
                fillOpacity=0.8
            ).add_to(procedure_group)
            
        # Add connecting lines
        coordinates = [[coord.latitude, coord.longitude] for _, coord in valid_coords]
        
        folium.PolyLine(
            locations=coordinates,
            color=color,
            weight=3,
            opacity=0.8,
            popup=f"{procedure.procedure_type}: {procedure.procedure_id}"
        ).add_to(procedure_group)
        
        # Add arrows to show direction
        for i in range(len(coordinates) - 1):
            start = coordinates[i]
            end = coordinates[i + 1]
            
            # Calculate midpoint for arrow placement
            mid_lat = (start[0] + end[0]) / 2
            mid_lon = (start[1] + end[1]) / 2
            
            folium.Marker(
                location=[mid_lat, mid_lon],
                icon=folium.DivIcon(
                    html=f'<div style="color: {color}; font-size: 12px;">→</div>',
                    icon_size=(10, 10),
                    icon_anchor=(5, 5)
                )
            ).add_to(procedure_group)
            
        procedure_group.add_to(self.map)
        
    def add_procedures(self, procedures: List[Procedure]) -> None:
        """Add multiple procedures to the map with different colors"""
        if not self.map:
            raise ValueError("Map not initialized. Call create_map() first.")
            
        colors = ['red', 'blue', 'green', 'purple', 'orange', 'darkred', 
                 'lightred', 'beige', 'darkblue', 'darkgreen', 'cadetblue', 
                 'darkpurple', 'white', 'pink', 'lightblue', 'lightgreen', 
                 'gray', 'black', 'lightgray']
        
        for i, procedure in enumerate(procedures):
            color = colors[i % len(colors)]
            self.add_procedure(procedure, color)
            
    def save_map(self, filename: str) -> None:
        """Save the map to an HTML file"""
        if not self.map:
            raise ValueError("Map not initialized. Call create_map() first.")
            
        self.map.save(filename)
        print(f"Map saved to {filename}")
        
    def get_map_bounds(self, airports: List[Airport] = None, 
                      waypoints: List[Waypoint] = None) -> Tuple[float, float, float, float]:
        """Calculate map bounds for the given data"""
        all_coords = []
        
        if airports:
            all_coords.extend([(a.coordinate.latitude, a.coordinate.longitude) for a in airports])
            
        if waypoints:
            all_coords.extend([(w.coordinate.latitude, w.coordinate.longitude) for w in waypoints])
            
        if not all_coords:
            return 39.8283, -98.5795, 39.8283, -98.5795  # Default US center
            
        lats = [coord[0] for coord in all_coords]
        lons = [coord[1] for coord in all_coords]
        
        return min(lats), min(lons), max(lats), max(lons)
        
    def fit_map_to_data(self, airports: List[Airport] = None, 
                       waypoints: List[Waypoint] = None) -> None:
        """Adjust map view to fit all data"""
        if not self.map:
            raise ValueError("Map not initialized. Call create_map() first.")
            
        min_lat, min_lon, max_lat, max_lon = self.get_map_bounds(airports, waypoints)
        
        # Add some padding
        lat_padding = (max_lat - min_lat) * 0.1
        lon_padding = (max_lon - min_lon) * 0.1
        
        self.map.fit_bounds([
            [min_lat - lat_padding, min_lon - lon_padding],
            [max_lat + lat_padding, max_lon + lon_padding]
        ])
        
    def add_region_summary(self, region: str) -> None:
        """Add a summary box for the region"""
        if not self.map:
            raise ValueError("Map not initialized. Call create_map() first.")
            
        airports_in_region = self.parser.get_airports_in_region(region)
        procedures_in_region = self.parser.get_procedures_for_airports(
            [a.icao for a in airports_in_region]
        )
        
        summary_html = f"""
        <div style="position: fixed; 
                    top: 10px; right: 10px; width: 200px; height: 120px; 
                    background-color: white; border:2px solid grey; z-index:9999; 
                    font-size:14px; padding: 10px">
        <h4>Region: {region}</h4>
        <p><b>Airports:</b> {len(airports_in_region)}</p>
        <p><b>Procedures:</b> {len(procedures_in_region)}</p>
        <p><b>SIDs:</b> {len([p for p in procedures_in_region if p.procedure_type == 'SID'])}</p>
        <p><b>STARs:</b> {len([p for p in procedures_in_region if p.procedure_type == 'STAR'])}</p>
        </div>
        """
        
        self.map.get_root().html.add_child(folium.Element(summary_html))
