#!/usr/bin/env python3
"""
Simple ARINC424 Map Tool

A reliable version that creates clean HTML maps without heavy dependencies.
"""

import argparse
import sys
import os
from typing import List

from arinc424_parser import ARINC424Parser
from simple_map_visualizer import SimpleMapVisualizer


def main():
    parser = argparse.ArgumentParser(
        description="Create reliable ARINC424 maps",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Show Atlanta airport with all procedures
  python3 simple_arinc424_tool.py flyright.pc --icao KATL --show-all
  
  # Show airports in USA region
  python3 simple_arinc424_tool.py flyright.pc --region USA --show-airports
  
  # Show SIDs for multiple airports
  python3 simple_arinc424_tool.py flyright.pc --icao KATL KLAX KORD --show-procedures --procedure-types SID
        """
    )
    
    # Required arguments
    parser.add_argument('file', help='ARINC424 file to parse (e.g., flyright.pc)')
    
    # Filtering options
    parser.add_argument('--region', help='Filter by region code (e.g., USA, CAN)')
    parser.add_argument('--icao', nargs='+', help='Filter by ICAO airport codes (e.g., KATL KORD)')
    
    # Display options
    parser.add_argument('--show-airports', action='store_true', 
                       help='Show airports on the map')
    parser.add_argument('--show-waypoints', action='store_true',
                       help='Show waypoints on the map')
    parser.add_argument('--show-procedures', action='store_true',
                       help='Show SID/STAR procedures on the map')
    parser.add_argument('--show-all', action='store_true',
                       help='Show airports, waypoints, and procedures')
    
    # Procedure filtering
    parser.add_argument('--procedure-types', nargs='+', 
                       choices=['SID', 'STAR', 'APPROACH'],
                       default=['SID', 'STAR'],
                       help='Types of procedures to show (default: SID STAR)')
    
    # Output options
    parser.add_argument('--output', '-o', default='arinc424_simple_map.html',
                       help='Output HTML file name (default: arinc424_simple_map.html)')
    
    # Information options
    parser.add_argument('--list-regions', action='store_true',
                       help='List all available regions and exit')
    parser.add_argument('--list-airports', action='store_true',
                       help='List airports in the specified region/ICAOs and exit')
    
    args = parser.parse_args()
    
    # Check if file exists
    if not os.path.exists(args.file):
        print(f"Error: File '{args.file}' not found.")
        sys.exit(1)
    
    # Parse the ARINC424 file
    print("Parsing ARINC424 file...")
    arinc_parser = ARINC424Parser()
    arinc_parser.parse_file(args.file)
    
    # Handle information requests
    if args.list_regions:
        regions = arinc_parser.get_available_regions()
        print(f"\nAvailable regions ({len(regions)}):")
        for region in regions:
            airports_count = len(arinc_parser.get_airports_in_region(region))
            print(f"  {region}: {airports_count} airports")
        return
    
    # Determine which airports to work with
    target_airports = []
    
    if args.region:
        print(f"Filtering by region: {args.region}")
        region_airports = arinc_parser.get_airports_in_region(args.region)
        target_airports.extend(region_airports)
        
    if args.icao:
        print(f"Filtering by ICAO codes: {', '.join(args.icao)}")
        icao_airports = []
        for icao in args.icao:
            if icao in arinc_parser.airports:
                icao_airports.append(arinc_parser.airports[icao])
            else:
                print(f"Warning: Airport {icao} not found in data")
        target_airports.extend(icao_airports)
    
    # Remove duplicates
    target_airports = list({airport.icao: airport for airport in target_airports}.values())
    
    if not target_airports and not args.list_airports:
        print("Error: No airports found matching the specified criteria.")
        print("Use --list-regions to see available regions.")
        sys.exit(1)
    
    if args.list_airports:
        print(f"\nFound {len(target_airports)} airports:")
        for airport in sorted(target_airports, key=lambda x: x.icao):
            print(f"  {airport.icao}: {airport.name} (Region: {airport.region})")
        return
    
    # Determine what to show
    show_airports = args.show_airports or args.show_all
    show_waypoints = args.show_waypoints or args.show_all
    show_procedures = args.show_procedures or args.show_all
    
    if not (show_airports or show_waypoints or show_procedures):
        print("Error: Must specify what to show (--show-airports, --show-waypoints, --show-procedures, or --show-all)")
        sys.exit(1)
    
    # Get procedures for target airports
    procedures = []
    if show_procedures:
        airport_icaos = [a.icao for a in target_airports]
        procedures = arinc_parser.get_procedures_for_airports(airport_icaos, args.procedure_types)
        print(f"Found {len(procedures)} procedures")
    
    # Get waypoints if requested
    waypoints = []
    if show_waypoints:
        # Get all waypoints referenced by procedures
        waypoint_ids = set()
        for proc in procedures:
            for step in proc.steps:
                waypoint_ids.add(step.waypoint_id)
        
        # Find waypoints in the parser's data
        for wp_key, waypoint in arinc_parser.waypoints.items():
            if waypoint.identifier in waypoint_ids:
                waypoints.append(waypoint)
        
        print(f"Found {len(waypoints)} waypoints")
    
    # Create the map
    print("Creating simple map...")
    visualizer = SimpleMapVisualizer(arinc_parser)
    
    # Prepare data for visualization
    airports_to_show = target_airports if show_airports else []
    procedures_to_show = procedures if show_procedures else []
    waypoints_to_show = waypoints if show_waypoints else []
    
    # Create the map
    visualizer.create_map(
        airports=airports_to_show,
        procedures=procedures_to_show,
        waypoints=waypoints_to_show,
        output_file=args.output
    )
    
    # Print summary
    print(f"\nSimple map created successfully!")
    print(f"Output file: {args.output}")
    print(f"Airports shown: {len(airports_to_show)}")
    print(f"Procedures shown: {len(procedures_to_show)}")
    print(f"Waypoints shown: {len(waypoints_to_show)}")
    
    if show_procedures:
        sid_count = len([p for p in procedures_to_show if p.procedure_type == 'SID'])
        star_count = len([p for p in procedures_to_show if p.procedure_type == 'STAR'])
        approach_count = len([p for p in procedures_to_show if p.procedure_type == 'APPROACH'])
        print(f"  - SIDs: {sid_count}")
        print(f"  - STARs: {star_count}")
        print(f"  - Approaches: {approach_count}")
    
    print(f"\nOpen {args.output} in a web browser to view the map.")


if __name__ == "__main__":
    main()
