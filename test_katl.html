<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_190812e310ec521b5d96a51f4b9e0635 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_190812e310ec521b5d96a51f4b9e0635" ></div>
        
</body>
<script>
    
    
            var map_190812e310ec521b5d96a51f4b9e0635 = L.map(
                "map_190812e310ec521b5d96a51f4b9e0635",
                {
                    center: [33.6367, -842.9000861111111],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 6,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_22d40193aecafb6023efbe66b95fa50b = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_22d40193aecafb6023efbe66b95fa50b.addTo(map_190812e310ec521b5d96a51f4b9e0635);
        
    
            var tile_layer_fc22cd81b28a66d371434f28210332e9 = L.tileLayer(
                "https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 18,
  "maxNativeZoom": 18,
  "noWrap": false,
  "attribution": "Map tiles by Stamen Design, CC BY 3.0 \u2014 Map data \u00a9 OpenStreetMap contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_fc22cd81b28a66d371434f28210332e9.addTo(map_190812e310ec521b5d96a51f4b9e0635);
        
    
            var tile_layer_63acd990b0ffc532e0934cb88a8fb81c = L.tileLayer(
                "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
                {
  "minZoom": 0,
  "maxZoom": 20,
  "maxNativeZoom": 20,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors \u0026copy; \u003ca href=\"https://carto.com/attributions\"\u003eCARTO\u003c/a\u003e",
  "subdomains": "abcd",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_63acd990b0ffc532e0934cb88a8fb81c.addTo(map_190812e310ec521b5d96a51f4b9e0635);
        
    
            var layer_control_4fe8868d62f3f28ecbb8815643e482f0_layers = {
                base_layers : {
                    "openstreetmap" : tile_layer_22d40193aecafb6023efbe66b95fa50b,
                    "Stamen Terrain" : tile_layer_fc22cd81b28a66d371434f28210332e9,
                    "CartoDB Positron" : tile_layer_63acd990b0ffc532e0934cb88a8fb81c,
                },
                overlays :  {
                    "Airports" : feature_group_2fa5f3b0548e5649136fa05f5af95d94,
                },
            };
            let layer_control_4fe8868d62f3f28ecbb8815643e482f0 = L.control.layers(
                layer_control_4fe8868d62f3f28ecbb8815643e482f0_layers.base_layers,
                layer_control_4fe8868d62f3f28ecbb8815643e482f0_layers.overlays,
                {
  "position": "topright",
  "collapsed": true,
  "autoZIndex": true,
}
            ).addTo(map_190812e310ec521b5d96a51f4b9e0635);

        
    
            var feature_group_2fa5f3b0548e5649136fa05f5af95d94 = L.featureGroup(
                {
}
            );
        
    
            var marker_fcec11e3eb4a1d5c36d592ffb73e7fe4 = L.marker(
                [33.6367, -842.9000861111111],
                {
}
            ).addTo(feature_group_2fa5f3b0548e5649136fa05f5af95d94);
        
    
            var icon_2a5e3b91d442d57faf455fd4c29fc030 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "plane",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_a61278090690d4affe8e9142e87ffdf2 = L.popup({
  "maxWidth": 300,
});

        
            
                var html_23ba56ce359d1553a65e47a18b1bfc79 = $(`<div id="html_23ba56ce359d1553a65e47a18b1bfc79" style="width: 100.0%; height: 100.0%;">             <b>HARTSFIELD - JACKSON ATLANTA I</b><br>             ICAO: KATL<br>             Region: USA<br>             Coordinates: 33.636700, -842.900086             </div>`)[0];
                popup_a61278090690d4affe8e9142e87ffdf2.setContent(html_23ba56ce359d1553a65e47a18b1bfc79);
            
        

        marker_fcec11e3eb4a1d5c36d592ffb73e7fe4.bindPopup(popup_a61278090690d4affe8e9142e87ffdf2)
        ;

        
    
    
            marker_fcec11e3eb4a1d5c36d592ffb73e7fe4.bindTooltip(
                `<div>
                     KATL - HARTSFIELD - JACKSON ATLANTA I
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_fcec11e3eb4a1d5c36d592ffb73e7fe4.setIcon(icon_2a5e3b91d442d57faf455fd4c29fc030);
            
    
            feature_group_2fa5f3b0548e5649136fa05f5af95d94.addTo(map_190812e310ec521b5d96a51f4b9e0635);
        
    
            map_190812e310ec521b5d96a51f4b9e0635.fitBounds(
                [[33.6367, -842.9000861111111], [33.6367, -842.9000861111111]],
                {}
            );
        
</script>
</html>